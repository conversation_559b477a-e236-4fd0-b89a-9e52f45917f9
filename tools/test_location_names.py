#!/usr/bin/env python3
"""
Test script for location_name parameter usage in DataForSEO client
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.direct_dataforseo_client import (
    call_dataforseo_api_endpoint,
    labs_google_related_keywords,
    serp_google_organic_live,
    rag_dataforseo_query
)

def test_location_names():
    """Test that location_name parameter is working correctly"""
    print("Testing Location Names in DataForSEO Client...")
    
    # Test 1: Direct API call with location_name
    print("\n1. Testing direct API call with location_name...")
    try:
        result = call_dataforseo_api_endpoint(
            api_endpoint_path="/dataforseo_labs/google/related_keywords/live",
            keyword="digital marketing",
            location_name="United Kingdom",
            language_code="en",
            search_limit=3
        )
        
        if 'error' in result:
            print(f"   ❌ API call failed: {result['error']}")
        else:
            print(f"   ✅ API call successful with location_name='United Kingdom'")
            
            # Check if the request was made with location_name
            if 'tasks' in result and result['tasks']:
                task = result['tasks'][0]
                if 'data' in task:
                    data = task['data']
                    if 'location_name' in data:
                        print(f"   ✅ Request used location_name: {data['location_name']}")
                    elif 'location_code' in data:
                        print(f"   ⚠️  Request used location_code: {data['location_code']}")
                    else:
                        print(f"   ⚠️  No location parameter found in request data")
                        
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Related keywords with different locations
    print("\n2. Testing related keywords with different location names...")
    test_locations = [
        "United States",
        "Germany", 
        "France",
        "Canada"
    ]
    
    for location in test_locations:
        try:
            result = labs_google_related_keywords(
                keyword="seo tools",
                location_name=location,
                limit=2
            )
            
            if 'error' in result:
                print(f"   ❌ {location}: {result['error']}")
            else:
                print(f"   ✅ {location}: API call successful")
                
        except Exception as e:
            print(f"   ❌ {location}: Exception - {e}")
    
    # Test 3: SERP results with location names
    print("\n3. Testing SERP results with location names...")
    try:
        result = serp_google_organic_live(
            keyword="best restaurants",
            location_name="New York",
            language_code="en"
        )
        
        if 'error' in result:
            print(f"   ❌ SERP call failed: {result['error']}")
        else:
            print(f"   ✅ SERP call successful with location_name='New York'")
            
    except Exception as e:
        print(f"   ❌ SERP exception: {e}")
    
    # Test 4: RAG query with location preference
    print("\n4. Testing RAG query with location preference...")
    try:
        result = rag_dataforseo_query(
            user_query="find related keywords for digital marketing",
            target_keyword="digital marketing",
            location_name="Australia",
            result_limit=3
        )
        
        if 'error' in result:
            print(f"   ❌ RAG query failed: {result['error']}")
        else:
            print(f"   ✅ RAG query successful with location_name='Australia'")
            
            # Check parameters used
            params_used = result.get('parameters_used', {})
            if 'location_name' in params_used:
                print(f"   ✅ RAG used location_name: {params_used['location_name']}")
            elif 'location_code' in params_used:
                print(f"   ⚠️  RAG used location_code: {params_used['location_code']}")
                
    except Exception as e:
        print(f"   ❌ RAG exception: {e}")
    
    # Test 5: Show parameter structure
    print("\n5. Showing parameter structure for verification...")
    try:
        # Make a simple call and inspect the actual request
        result = call_dataforseo_api_endpoint(
            api_endpoint_path="/dataforseo_labs/google/related_keywords/live",
            keyword="test keyword",
            location_name="Spain",
            search_limit=1
        )
        
        if 'tasks' in result and result['tasks']:
            task = result['tasks'][0]
            print(f"   Request data structure:")
            if 'data' in task:
                data = task['data']
                for key, value in data.items():
                    if key in ['location_name', 'location_code', 'keyword', 'language_code']:
                        print(f"     {key}: {value}")
                        
    except Exception as e:
        print(f"   ❌ Parameter inspection failed: {e}")
    
    print("\n✅ Location name testing complete!")

if __name__ == "__main__":
    test_location_names()
