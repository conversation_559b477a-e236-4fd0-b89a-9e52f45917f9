#!/usr/bin/env python3
"""
Test script for DataForSEO RAG system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.dataforseo_rag_system import DataForSEODocumentationRAG, get_rag_system
import json

def test_rag_system():
    """Test the RAG system"""
    print("Testing DataForSEO RAG System...")
    
    # Test RAG system initialization
    print("\n1. Testing RAG system initialization...")
    try:
        rag = DataForSEODocumentationRAG()
        print(f"✅ RAG system initialized with {len(rag.documents)} documents")
        
        if rag.model:
            print(f"✅ Embedding model loaded: {rag.model_name}")
        else:
            print("⚠️  Using fallback search (no embeddings)")
            
    except Exception as e:
        print(f"❌ Failed to initialize RAG system: {e}")
        return
    
    # Test semantic search
    print("\n2. Testing semantic search...")
    test_queries = [
        "find related keywords for content marketing",
        "get search volume data for keywords",
        "analyze competitor domain rankings",
        "get Google search results for keyword",
        "check backlink profile of website"
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        try:
            results = rag.semantic_search(query, top_k=3)
            print(f"  Found {len(results)} results:")
            
            for i, result in enumerate(results[:2]):  # Show top 2
                print(f"    {i+1}. {result.get('endpoint', 'N/A')}")
                print(f"       Title: {result.get('title', 'N/A')}")
                print(f"       Category: {result.get('category', 'N/A')}")
                if 'similarity_score' in result:
                    print(f"       Score: {result['similarity_score']:.3f}")
                    
        except Exception as e:
            print(f"    ❌ Search failed: {e}")
    
    # Test endpoint finding
    print("\n3. Testing best endpoint finding...")
    test_queries_endpoint = [
        "I want to get related keywords",
        "Show me Google search results",
        "Get keyword difficulty scores",
        "Analyze backlinks for a domain"
    ]
    
    for query in test_queries_endpoint:
        print(f"\nQuery: '{query}'")
        try:
            result = rag.find_best_endpoint(query)
            if 'error' in result:
                print(f"    ❌ {result['error']}")
            else:
                print(f"    ✅ Best endpoint: {result.get('endpoint', 'N/A')}")
                print(f"       Confidence: {result.get('confidence', 0):.3f}")
                print(f"       Reasoning: {result.get('reasoning', 'N/A')}")
                
        except Exception as e:
            print(f"    ❌ Endpoint finding failed: {e}")
    
    # Test global instance
    print("\n4. Testing global RAG instance...")
    try:
        global_rag = get_rag_system()
        print(f"✅ Global RAG instance: {len(global_rag.documents)} documents")
        
        # Quick search test
        results = global_rag.semantic_search("keyword research", top_k=1)
        if results:
            print(f"✅ Global search works: {results[0].get('endpoint', 'N/A')}")
        else:
            print("⚠️  Global search returned no results")
            
    except Exception as e:
        print(f"❌ Global RAG instance failed: {e}")
    
    print("\n✅ RAG system testing complete!")

if __name__ == "__main__":
    test_rag_system()
