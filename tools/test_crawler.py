#!/usr/bin/env python3
"""
Test script for DataForSEO documentation crawler
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.dataforseo_rag_system import DataForSEODocumentationCrawler
import j<PERSON>

def test_crawler():
    """Test the documentation crawler"""
    print("Testing DataForSEO Documentation Crawler...")
    
    crawler = DataForSEODocumentationCrawler()
    
    # Test LLM documentation parsing
    print("\n1. Testing LLM documentation parsing...")
    try:
        llm_url = "https://docs.dataforseo.com/v3/llms.txt"
        crawler._crawl_llm_documentation(llm_url)
        print(f"✅ Successfully parsed LLM documentation: {len(crawler.documents)} sections")
        
        # Show first few documents
        for i, doc in enumerate(crawler.documents[:3]):
            print(f"\nDocument {i+1}:")
            print(f"  Endpoint: {doc['endpoint']}")
            print(f"  Title: {doc['title']}")
            print(f"  Category: {doc['category']}")
            print(f"  Description: {doc['description'][:100]}...")
            
    except Exception as e:
        print(f"❌ Failed to parse LLM documentation: {e}")
    
    # Test endpoint page crawling
    print("\n2. Testing endpoint page crawling...")
    try:
        test_url = "https://docs.dataforseo.com/v3/serp/google/organic/live/advanced/"
        initial_count = len(crawler.documents)
        crawler._crawl_endpoint_page(test_url)
        new_count = len(crawler.documents)
        
        if new_count > initial_count:
            print(f"✅ Successfully crawled endpoint page: added {new_count - initial_count} documents")
            
            # Show the new document
            new_doc = crawler.documents[-1]
            print(f"\nNew Document:")
            print(f"  Endpoint: {new_doc['endpoint']}")
            print(f"  Title: {new_doc['title']}")
            print(f"  Category: {new_doc['category']}")
            print(f"  Parameters: {len(new_doc.get('parameters', []))}")
            print(f"  Examples: {len(new_doc.get('examples', []))}")
        else:
            print("⚠️  No new documents added from endpoint page")
            
    except Exception as e:
        print(f"❌ Failed to crawl endpoint page: {e}")
    
    print(f"\nTotal documents crawled: {len(crawler.documents)}")
    
    # Save sample output
    if crawler.documents:
        sample_output = {
            "total_documents": len(crawler.documents),
            "sample_documents": crawler.documents[:2]
        }
        
        with open("crawler_test_output.json", "w") as f:
            json.dump(sample_output, f, indent=2)
        print("📄 Sample output saved to crawler_test_output.json")

if __name__ == "__main__":
    test_crawler()
