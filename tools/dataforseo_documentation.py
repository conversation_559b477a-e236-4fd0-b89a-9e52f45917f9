#!/usr/bin/env python3
"""
DataForSEO API Documentation Context
Provides comprehensive API documentation to prevent LLM hallucination
"""

from typing import Dict, Any, List, Optional
import json

# Comprehensive DataForSEO API Documentation
DATAFORSEO_API_DOCUMENTATION = {
    "serp_apis": {
        "description": "Search Engine Results Page (SERP) analysis across multiple search engines",
        "endpoints": {
            "/serp/google/organic/live/advanced": {
                "description": "Get live Google organic search results with full SERP features",
                "required_params": ["keyword"],
                "optional_params": ["location_code", "language_code", "device", "os"],
                "use_cases": ["SERP analysis", "ranking monitoring", "competitor research"],
                "example": {
                    "keyword": "digital marketing",
                    "location_code": 2840,
                    "language_code": "en",
                    "device": "desktop"
                }
            },
            "/serp/google/maps/live/advanced": {
                "description": "Get live Google Maps search results for local SEO analysis",
                "required_params": ["keyword"],
                "optional_params": ["location_code", "language_code"],
                "use_cases": ["local SEO", "business listings", "map pack analysis"],
                "example": {
                    "keyword": "restaurants near me",
                    "location_code": 1023191
                }
            },
            "/serp/google/images/live/advanced": {
                "description": "Get live Google Images search results",
                "required_params": ["keyword"],
                "optional_params": ["location_code", "language_code"],
                "use_cases": ["image SEO", "visual content analysis"],
                "example": {
                    "keyword": "product photography",
                    "location_code": 2840
                }
            },
            "/serp/bing/organic/live/advanced": {
                "description": "Get live Bing organic search results",
                "required_params": ["keyword"],
                "optional_params": ["location_code", "language_code", "device"],
                "use_cases": ["Bing SEO", "alternative search engine analysis"],
                "example": {
                    "keyword": "microsoft products",
                    "location_code": 2840
                }
            }
        }
    },
    "dataforseo_labs": {
        "description": "Advanced SEO research tools with comprehensive metrics",
        "endpoints": {
            "/dataforseo_labs/google/related_keywords/live": {
                "description": "Get semantically related keywords with SEO metrics",
                "required_params": ["keyword"],
                "optional_params": ["location_code", "language_code", "limit"],
                "use_cases": ["keyword expansion", "semantic research", "content planning"],
                "example": {
                    "keyword": "content marketing",
                    "location_code": 2840,
                    "limit": 50
                }
            },
            "/dataforseo_labs/google/keyword_ideas/live": {
                "description": "Generate comprehensive keyword ideas for large-scale research",
                "required_params": ["keyword"],
                "optional_params": ["location_code", "language_code", "limit"],
                "use_cases": ["keyword discovery", "content strategy", "PPC research"],
                "example": {
                    "keyword": "digital marketing",
                    "location_code": 2840,
                    "limit": 1000
                }
            },
            "/dataforseo_labs/google/bulk_keyword_difficulty/live": {
                "description": "Get keyword difficulty scores for multiple keywords",
                "required_params": ["keywords"],
                "optional_params": ["location_code", "language_code"],
                "use_cases": ["keyword difficulty analysis", "SEO strategy planning"],
                "example": {
                    "keywords": ["seo tools", "keyword research", "digital marketing"],
                    "location_code": 2840
                }
            },
            "/dataforseo_labs/google/ranked_keywords/live": {
                "description": "Get keywords that a specific domain ranks for",
                "required_params": ["target"],
                "optional_params": ["location_code", "language_code", "limit"],
                "use_cases": ["competitor analysis", "domain research", "keyword gap analysis"],
                "example": {
                    "target": "semrush.com",
                    "location_code": 2840,
                    "limit": 100
                }
            },
            "/dataforseo_labs/google/domain_rank_overview/live": {
                "description": "Get comprehensive domain ranking metrics and overview",
                "required_params": ["target"],
                "optional_params": ["location_code", "language_code"],
                "use_cases": ["domain authority analysis", "competitor benchmarking"],
                "example": {
                    "target": "ahrefs.com",
                    "location_code": 2840
                }
            }
        }
    },
    "keywords_data": {
        "description": "Keyword metrics and search volume data from Google Ads",
        "endpoints": {
            "/keywords_data/google_ads/search_volume/live": {
                "description": "Get search volume data for keywords from Google Ads",
                "required_params": ["keywords"],
                "optional_params": ["location_code", "language_code"],
                "use_cases": ["search volume analysis", "PPC planning", "keyword validation"],
                "example": {
                    "keywords": ["seo", "digital marketing", "content strategy"],
                    "location_code": 2840
                }
            },
            "/keywords_data/google_ads/keywords_for_keywords/live": {
                "description": "Get related keywords from Google Ads keyword planner",
                "required_params": ["keywords"],
                "optional_params": ["location_code", "language_code"],
                "use_cases": ["keyword expansion", "PPC research", "ad group planning"],
                "example": {
                    "keywords": ["digital marketing"],
                    "location_code": 2840
                }
            }
        }
    },
    "backlinks": {
        "description": "Comprehensive backlink analysis and link building research",
        "endpoints": {
            "/backlinks/summary/live": {
                "description": "Get backlink summary and metrics for a domain",
                "required_params": ["target"],
                "optional_params": ["limit"],
                "use_cases": ["backlink analysis", "domain authority assessment", "link building"],
                "example": {
                    "target": "moz.com",
                    "limit": 100
                }
            },
            "/backlinks/anchors/live": {
                "description": "Get anchor text analysis for a domain's backlinks",
                "required_params": ["target"],
                "optional_params": ["limit"],
                "use_cases": ["anchor text analysis", "link profile assessment"],
                "example": {
                    "target": "example.com",
                    "limit": 50
                }
            },
            "/backlinks/referring_domains/live": {
                "description": "Get referring domains linking to a target domain",
                "required_params": ["target"],
                "optional_params": ["limit"],
                "use_cases": ["referring domain analysis", "link prospecting"],
                "example": {
                    "target": "hubspot.com",
                    "limit": 100
                }
            }
        }
    },
    "on_page": {
        "description": "On-page SEO analysis and technical SEO auditing",
        "endpoints": {
            "/on_page/summary/live": {
                "description": "Get comprehensive on-page SEO summary for a domain",
                "required_params": ["target"],
                "optional_params": [],
                "use_cases": ["technical SEO audit", "on-page optimization", "site health check"],
                "example": {
                    "target": "example.com"
                }
            },
            "/on_page/pages/live": {
                "description": "Get detailed page-level analysis for a domain",
                "required_params": ["target"],
                "optional_params": ["limit"],
                "use_cases": ["page-level SEO analysis", "content optimization"],
                "example": {
                    "target": "example.com",
                    "limit": 100
                }
            }
        }
    },
    "domain_analytics": {
        "description": "Domain analytics, technologies, and WHOIS information",
        "endpoints": {
            "/domain_analytics/google/overview/live": {
                "description": "Get comprehensive domain analytics overview",
                "required_params": ["target"],
                "optional_params": ["location_code", "language_code"],
                "use_cases": ["domain analysis", "competitor research", "market analysis"],
                "example": {
                    "target": "shopify.com",
                    "location_code": 2840
                }
            },
            "/domain_analytics/google/technologies/live": {
                "description": "Get technologies and tools used by a domain",
                "required_params": ["target"],
                "optional_params": [],
                "use_cases": ["technology stack analysis", "competitor tech research"],
                "example": {
                    "target": "netflix.com"
                }
            },
            "/domain_analytics/google/whois/live": {
                "description": "Get WHOIS information for a domain",
                "required_params": ["target"],
                "optional_params": [],
                "use_cases": ["domain ownership research", "registration analysis"],
                "example": {
                    "target": "google.com"
                }
            }
        }
    },
    "common_parameters": {
        "location_codes": {
            "2840": "United States",
            "2826": "United Kingdom", 
            "2276": "Germany",
            "2250": "France",
            "2724": "Spain",
            "2380": "Italy",
            "2124": "Canada",
            "2036": "Australia",
            "1023191": "New York, NY, USA",
            "1023768": "Los Angeles, CA, USA",
            "2340": "Kenya"
        },
        "language_codes": {
            "en": "English",
            "es": "Spanish",
            "fr": "French",
            "de": "German",
            "it": "Italian",
            "pt": "Portuguese",
            "ru": "Russian",
            "ja": "Japanese",
            "zh": "Chinese"
        },
        "device_types": ["desktop", "mobile", "tablet"],
        "operating_systems": ["windows", "macos", "linux", "android", "ios"]
    }
}

def find_best_endpoint(user_query: str) -> Dict[str, Any]:
    """
    Find the best DataForSEO endpoint based on user query
    
    Args:
        user_query: Natural language query from user
        
    Returns:
        Dict with recommended endpoint, parameters, and reasoning
    """
    query_lower = user_query.lower()
    
    # Keyword research patterns
    if any(term in query_lower for term in ['related keywords', 'keyword ideas', 'keyword research', 'expand keywords']):
        if 'related' in query_lower or 'similar' in query_lower:
            return {
                "endpoint": "/dataforseo_labs/google/related_keywords/live",
                "category": "dataforseo_labs",
                "reasoning": "User wants related/similar keywords",
                "required_params": ["keyword"],
                "suggested_params": {"limit": 50}
            }
        else:
            return {
                "endpoint": "/dataforseo_labs/google/keyword_ideas/live", 
                "category": "dataforseo_labs",
                "reasoning": "User wants comprehensive keyword ideas",
                "required_params": ["keyword"],
                "suggested_params": {"limit": 100}
            }
    
    # Search volume patterns
    if any(term in query_lower for term in ['search volume', 'volume data', 'monthly searches']):
        return {
            "endpoint": "/keywords_data/google_ads/search_volume/live",
            "category": "keywords_data", 
            "reasoning": "User wants search volume data",
            "required_params": ["keywords"],
            "suggested_params": {}
        }
    
    # Keyword difficulty patterns
    if any(term in query_lower for term in ['keyword difficulty', 'difficulty score', 'how hard']):
        return {
            "endpoint": "/dataforseo_labs/google/bulk_keyword_difficulty/live",
            "category": "dataforseo_labs",
            "reasoning": "User wants keyword difficulty analysis", 
            "required_params": ["keywords"],
            "suggested_params": {}
        }
    
    # SERP analysis patterns
    if any(term in query_lower for term in ['serp', 'search results', 'google results', 'ranking']):
        if 'maps' in query_lower or 'local' in query_lower:
            return {
                "endpoint": "/serp/google/maps/live/advanced",
                "category": "serp_apis",
                "reasoning": "User wants Google Maps/local search results",
                "required_params": ["keyword"],
                "suggested_params": {}
            }
        elif 'images' in query_lower:
            return {
                "endpoint": "/serp/google/images/live/advanced", 
                "category": "serp_apis",
                "reasoning": "User wants Google Images results",
                "required_params": ["keyword"],
                "suggested_params": {}
            }
        else:
            return {
                "endpoint": "/serp/google/organic/live/advanced",
                "category": "serp_apis", 
                "reasoning": "User wants Google organic search results",
                "required_params": ["keyword"],
                "suggested_params": {"device": "desktop"}
            }
    
    # Competitor analysis patterns
    if any(term in query_lower for term in ['competitor', 'domain ranks', 'what keywords does', 'ranks for']):
        return {
            "endpoint": "/dataforseo_labs/google/ranked_keywords/live",
            "category": "dataforseo_labs",
            "reasoning": "User wants competitor keyword analysis",
            "required_params": ["target"],
            "suggested_params": {"limit": 100}
        }
    
    # Domain analysis patterns  
    if any(term in query_lower for term in ['domain analysis', 'domain overview', 'domain metrics']):
        return {
            "endpoint": "/dataforseo_labs/google/domain_rank_overview/live",
            "category": "dataforseo_labs", 
            "reasoning": "User wants domain ranking overview",
            "required_params": ["target"],
            "suggested_params": {}
        }
    
    # Backlink patterns
    if any(term in query_lower for term in ['backlinks', 'link analysis', 'referring domains']):
        return {
            "endpoint": "/backlinks/summary/live",
            "category": "backlinks",
            "reasoning": "User wants backlink analysis", 
            "required_params": ["target"],
            "suggested_params": {"limit": 100}
        }
    
    # On-page SEO patterns
    if any(term in query_lower for term in ['on-page', 'technical seo', 'site audit', 'page analysis']):
        return {
            "endpoint": "/on_page/summary/live",
            "category": "on_page",
            "reasoning": "User wants on-page SEO analysis",
            "required_params": ["target"], 
            "suggested_params": {}
        }
    
    # Default fallback
    return {
        "endpoint": "/serp/google/organic/live/advanced",
        "category": "serp_apis",
        "reasoning": "Default SERP analysis - please specify your exact need",
        "required_params": ["keyword"],
        "suggested_params": {}
    }

def get_endpoint_documentation(endpoint: str) -> Optional[Dict[str, Any]]:
    """Get documentation for a specific endpoint"""
    for category_data in DATAFORSEO_API_DOCUMENTATION.values():
        if isinstance(category_data, dict) and "endpoints" in category_data:
            if endpoint in category_data["endpoints"]:
                return category_data["endpoints"][endpoint]
    return None

def validate_parameters(endpoint: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """Validate parameters against endpoint requirements"""
    doc = get_endpoint_documentation(endpoint)
    if not doc:
        return {"valid": False, "error": f"Unknown endpoint: {endpoint}"}
    
    # Check required parameters
    missing_params = []
    for required_param in doc["required_params"]:
        if required_param not in parameters:
            missing_params.append(required_param)
    
    if missing_params:
        return {
            "valid": False, 
            "error": f"Missing required parameters: {missing_params}",
            "required_params": doc["required_params"],
            "example": doc["example"]
        }
    
    return {"valid": True, "message": "Parameters are valid"}
