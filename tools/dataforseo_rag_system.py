#!/usr/bin/env python3
"""
DataForSEO RAG (Retrieval-Augmented Generation) System
Semantic search over DataForSEO API documentation to prevent LLM hallucination
"""

import os
import json
import pickle
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from smolagents import tool
import logging

# Try to import sentence transformers for embeddings
try:
    from sentence_transformers import SentenceTransformer
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    print("⚠️  sentence-transformers not installed. Install with: pip install sentence-transformers")

# Try to load .env file if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

logger = logging.getLogger(__name__)

class DataForSEODocumentationRAG:
    """RAG system for DataForSEO API documentation"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model_name = model_name
        self.model = None
        self.documents = []
        self.embeddings = None
        self.index_file = "dataforseo_doc_index.pkl"
        
        if EMBEDDINGS_AVAILABLE:
            self.model = SentenceTransformer(model_name)
            self._load_or_create_index()
        else:
            logger.warning("Sentence transformers not available. Using fallback search.")
    
    def _load_or_create_index(self):
        """Load existing index or create new one"""
        if os.path.exists(self.index_file):
            try:
                with open(self.index_file, 'rb') as f:
                    data = pickle.load(f)
                    self.documents = data['documents']
                    self.embeddings = data['embeddings']
                logger.info(f"Loaded existing documentation index with {len(self.documents)} documents")
                return
            except Exception as e:
                logger.warning(f"Failed to load index: {e}. Creating new one.")
        
        self._create_documentation_index()
    
    def _create_documentation_index(self):
        """Create semantic search index from DataForSEO documentation"""
        from .dataforseo_documentation import DATAFORSEO_API_DOCUMENTATION
        
        self.documents = []
        
        # Process each endpoint into searchable documents
        for category_name, category_data in DATAFORSEO_API_DOCUMENTATION.items():
            if not isinstance(category_data, dict) or "endpoints" not in category_data:
                continue
                
            category_desc = category_data.get("description", "")
            
            for endpoint, endpoint_data in category_data["endpoints"].items():
                # Create comprehensive document for each endpoint
                doc = {
                    "endpoint": endpoint,
                    "category": category_name,
                    "category_description": category_desc,
                    "description": endpoint_data["description"],
                    "required_params": endpoint_data["required_params"],
                    "optional_params": endpoint_data.get("optional_params", []),
                    "use_cases": endpoint_data["use_cases"],
                    "example": endpoint_data["example"],
                    "search_text": self._create_search_text(endpoint, endpoint_data, category_desc)
                }
                self.documents.append(doc)
        
        # Create embeddings for all documents
        if self.model:
            search_texts = [doc["search_text"] for doc in self.documents]
            self.embeddings = self.model.encode(search_texts)
            
            # Save index for future use
            try:
                with open(self.index_file, 'wb') as f:
                    pickle.dump({
                        'documents': self.documents,
                        'embeddings': self.embeddings
                    }, f)
                logger.info(f"Created and saved documentation index with {len(self.documents)} documents")
            except Exception as e:
                logger.warning(f"Failed to save index: {e}")
    
    def _create_search_text(self, endpoint: str, endpoint_data: Dict, category_desc: str) -> str:
        """Create comprehensive search text for an endpoint"""
        parts = [
            endpoint,
            endpoint_data["description"],
            category_desc,
            " ".join(endpoint_data["use_cases"]),
            " ".join(endpoint_data["required_params"]),
            " ".join(endpoint_data.get("optional_params", [])),
        ]
        
        # Add example parameters as searchable text
        if "example" in endpoint_data:
            example_text = " ".join([f"{k} {v}" for k, v in endpoint_data["example"].items()])
            parts.append(example_text)
        
        return " ".join(parts).lower()
    
    def semantic_search(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Perform semantic search over documentation"""
        if not EMBEDDINGS_AVAILABLE or self.model is None:
            return self._fallback_search(query, top_k)
        
        # Encode the query
        query_embedding = self.model.encode([query.lower()])
        
        # Calculate similarities
        similarities = np.dot(query_embedding, self.embeddings.T)[0]
        
        # Get top-k most similar documents
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            doc = self.documents[idx].copy()
            doc["similarity_score"] = float(similarities[idx])
            results.append(doc)
        
        return results
    
    def _fallback_search(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Fallback keyword-based search when embeddings not available"""
        query_words = set(query.lower().split())
        
        scored_docs = []
        for doc in self.documents:
            search_words = set(doc["search_text"].split())
            overlap = len(query_words.intersection(search_words))
            if overlap > 0:
                score = overlap / len(query_words.union(search_words))
                doc_copy = doc.copy()
                doc_copy["similarity_score"] = score
                scored_docs.append(doc_copy)
        
        # Sort by score and return top-k
        scored_docs.sort(key=lambda x: x["similarity_score"], reverse=True)
        return scored_docs[:top_k]
    
    def find_best_endpoint(self, user_query: str) -> Dict[str, Any]:
        """Find the best endpoint for a user query using semantic search"""
        results = self.semantic_search(user_query, top_k=1)
        
        if not results:
            return {
                "error": "No matching endpoint found",
                "suggestion": "Please be more specific about your SEO data needs"
            }
        
        best_match = results[0]
        return {
            "endpoint": best_match["endpoint"],
            "category": best_match["category"],
            "confidence": best_match["similarity_score"],
            "description": best_match["description"],
            "required_params": best_match["required_params"],
            "optional_params": best_match["optional_params"],
            "use_cases": best_match["use_cases"],
            "example": best_match["example"],
            "reasoning": f"Semantic match (confidence: {best_match['similarity_score']:.3f})"
        }
    
    def get_endpoint_suggestions(self, user_query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Get multiple endpoint suggestions for a user query"""
        return self.semantic_search(user_query, top_k=top_k)

# Global RAG system instance
_rag_system = None

def get_rag_system() -> DataForSEODocumentationRAG:
    """Get or create the global RAG system instance"""
    global _rag_system
    if _rag_system is None:
        _rag_system = DataForSEODocumentationRAG()
    return _rag_system

@tool
def search_dataforseo_documentation(
    search_query: str,
    max_results: int = 3
) -> Dict[str, Any]:
    """
    Semantic search over DataForSEO API documentation to find relevant endpoints and parameters.
    This tool uses RAG (Retrieval-Augmented Generation) to prevent LLM hallucination by providing
    accurate, contextual information about available DataForSEO API endpoints.
    
    Args:
        search_query: Natural language description of what SEO data or analysis you need
        max_results: Maximum number of endpoint suggestions to return (default: 3)
        
    Returns:
        Dict containing:
        - best_match: The most relevant endpoint with full documentation
        - alternatives: Other relevant endpoints to consider
        - search_metadata: Information about the search process
        
    Examples:
        search_query="find related keywords for content marketing"
        search_query="get search volume data for multiple keywords"
        search_query="analyze competitor domain rankings"
        search_query="check backlink profile of a website"
        search_query="get Google Maps local search results"
    """
    rag_system = get_rag_system()
    
    # Get the best match
    best_match = rag_system.find_best_endpoint(search_query)
    
    # Get alternative suggestions
    all_suggestions = rag_system.get_endpoint_suggestions(search_query, max_results)
    alternatives = all_suggestions[1:] if len(all_suggestions) > 1 else []
    
    return {
        "search_query": search_query,
        "best_match": best_match,
        "alternatives": [
            {
                "endpoint": alt["endpoint"],
                "description": alt["description"],
                "confidence": alt["similarity_score"],
                "use_cases": alt["use_cases"]
            }
            for alt in alternatives
        ],
        "search_metadata": {
            "total_documents_searched": len(rag_system.documents),
            "embedding_model": rag_system.model_name if EMBEDDINGS_AVAILABLE else "keyword_fallback",
            "results_found": len(all_suggestions)
        }
    }

@tool  
def get_endpoint_documentation(
    endpoint_path: str
) -> Dict[str, Any]:
    """
    Get comprehensive documentation for a specific DataForSEO API endpoint.
    
    Args:
        endpoint_path: The DataForSEO API endpoint path (e.g., '/serp/google/organic/live/advanced')
        
    Returns:
        Dict containing complete endpoint documentation including parameters, examples, and use cases
    """
    rag_system = get_rag_system()
    
    # Find the document for this endpoint
    for doc in rag_system.documents:
        if doc["endpoint"] == endpoint_path:
            return {
                "endpoint": doc["endpoint"],
                "category": doc["category"],
                "description": doc["description"],
                "required_parameters": doc["required_params"],
                "optional_parameters": doc["optional_params"],
                "use_cases": doc["use_cases"],
                "example_request": doc["example"],
                "category_description": doc["category_description"]
            }
    
    return {
        "error": f"Endpoint '{endpoint_path}' not found in documentation",
        "suggestion": "Use search_dataforseo_documentation to find available endpoints"
    }
