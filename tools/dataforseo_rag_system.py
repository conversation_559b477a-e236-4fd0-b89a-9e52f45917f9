#!/usr/bin/env python3
"""
DataForSEO RAG (Retrieval-Augmented Generation) System
Semantic search over DataForSEO API documentation to prevent LLM hallucination
"""

import os
import json
import pickle
import re
import time
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from smolagents import tool
import logging
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

# Try to import sentence transformers for embeddings
try:
    from sentence_transformers import SentenceTransformer
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    print("⚠️  sentence-transformers not installed. Install with: pip install sentence-transformers")

# Try to load .env file if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

logger = logging.getLogger(__name__)

class DataForSEODocumentationCrawler:
    """Crawler for DataForSEO API documentation"""

    def __init__(self, base_url: str = "https://docs.dataforseo.com/v3"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DataForSEO-RAG-Crawler/1.0'
        })
        self.crawled_urls = set()
        self.documents = []

    def crawl_documentation(self) -> List[Dict[str, Any]]:
        """Crawl the entire DataForSEO documentation"""
        logger.info("Starting DataForSEO documentation crawl...")

        # Start with the LLM-friendly documentation
        llm_doc_url = f"{self.base_url}/llms.txt"
        self._crawl_llm_documentation(llm_doc_url)

        # Also crawl key endpoint pages for detailed parameter information
        key_endpoints = [
            "/serp/google/organic/live/advanced/",
            "/serp/google/maps/live/advanced/",
            "/dataforseo_labs/google/related_keywords/live/",
            "/dataforseo_labs/google/keyword_ideas/live/",
            "/dataforseo_labs/google/ranked_keywords/live/",
            "/keywords_data/google_ads/search_volume/live/",
            "/backlinks/summary/live/",
            "/on_page/summary/",
            "/domain_analytics/whois/overview/live/"
        ]

        for endpoint in key_endpoints:
            url = f"{self.base_url}{endpoint}"
            self._crawl_endpoint_page(url)
            time.sleep(0.5)  # Be respectful to the server

        logger.info(f"Crawled {len(self.documents)} documentation sections")
        return self.documents

    def _crawl_llm_documentation(self, url: str):
        """Crawl the LLM-friendly documentation format"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            content = response.text

            # Parse the LLM documentation format
            sections = self._parse_llm_documentation(content)
            self.documents.extend(sections)

        except Exception as e:
            logger.error(f"Failed to crawl LLM documentation: {e}")

    def _parse_llm_documentation(self, content: str) -> List[Dict[str, Any]]:
        """Parse the LLM-friendly documentation format"""
        documents = []

        # Split by major sections
        sections = content.split('## ')

        for section in sections[1:]:  # Skip the first empty section
            lines = section.strip().split('\n')
            if not lines:
                continue

            section_title = lines[0].strip()
            section_content = '\n'.join(lines[1:])

            # Extract endpoints from this section
            endpoint_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
            endpoints = re.findall(endpoint_pattern, section_content)

            for title, url in endpoints:
                # Extract endpoint path from URL
                endpoint_path = self._extract_endpoint_path(url)
                if endpoint_path:
                    doc = {
                        'endpoint': endpoint_path,
                        'title': title,
                        'category': section_title,
                        'description': self._extract_description(section_content, title),
                        'url': url,
                        'content': section_content,
                        'source': 'llm_documentation'
                    }
                    documents.append(doc)

        return documents

    def _extract_endpoint_path(self, url: str) -> str:
        """Extract API endpoint path from documentation URL"""
        # Convert documentation URL to API endpoint
        if '/v3/' not in url:
            return ""

        # Extract the path after /v3/
        path_part = url.split('/v3/')[-1]

        # Remove .md extension and convert to API path
        path_part = path_part.replace('.md', '')

        # Convert documentation paths to API paths
        api_path = f"/{path_part}"

        # Handle special cases
        if api_path.endswith('/live'):
            api_path += '/advanced'
        elif '/task_get/' in api_path and not api_path.endswith('/advanced'):
            api_path += '/advanced'

        return api_path

    def _extract_description(self, content: str, title: str) -> str:
        """Extract description for an endpoint from content"""
        lines = content.split('\n')
        description = ""

        # Look for description after the title
        for i, line in enumerate(lines):
            if title in line and i + 1 < len(lines):
                # Get the next few lines as description
                desc_lines = []
                for j in range(i + 1, min(i + 4, len(lines))):
                    if lines[j].strip() and not lines[j].startswith('['):
                        desc_lines.append(lines[j].strip())
                    else:
                        break
                description = ' '.join(desc_lines)
                break

        return description or "DataForSEO API endpoint"

    def _crawl_endpoint_page(self, url: str):
        """Crawl a specific endpoint documentation page"""
        if url in self.crawled_urls:
            return

        try:
            response = self.session.get(url)
            response.raise_for_status()
            self.crawled_urls.add(url)

            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract endpoint information
            endpoint_path = self._extract_endpoint_from_url(url)
            title = soup.find('h1')
            title_text = title.get_text().strip() if title else ""

            # Extract description
            description = self._extract_page_description(soup)

            # Extract parameters
            parameters = self._extract_parameters(soup)

            # Extract examples
            examples = self._extract_examples(soup)

            doc = {
                'endpoint': endpoint_path,
                'title': title_text,
                'category': self._extract_category_from_url(url),
                'description': description,
                'parameters': parameters,
                'examples': examples,
                'url': url,
                'content': soup.get_text()[:2000],  # Limit content size
                'source': 'endpoint_page'
            }

            self.documents.append(doc)

        except Exception as e:
            logger.error(f"Failed to crawl endpoint page {url}: {e}")

    def _extract_endpoint_from_url(self, url: str) -> str:
        """Extract API endpoint from documentation URL"""
        parsed = urlparse(url)
        path = parsed.path

        # Convert docs path to API path
        if '/v3/' in path:
            api_path = path.replace('/v3/', '/').rstrip('/')
            return api_path
        return ""

    def _extract_category_from_url(self, url: str) -> str:
        """Extract category from URL"""
        parts = url.split('/')
        if len(parts) >= 5:
            return parts[4]  # e.g., 'serp', 'dataforseo_labs', etc.
        return "unknown"

    def _extract_page_description(self, soup: BeautifulSoup) -> str:
        """Extract description from page"""
        # Look for description in various places
        desc_selectors = [
            'p:first-of-type',
            '.description',
            'div.content p:first-child'
        ]

        for selector in desc_selectors:
            desc_elem = soup.select_one(selector)
            if desc_elem:
                return desc_elem.get_text().strip()[:500]

        return "DataForSEO API endpoint"

    def _extract_parameters(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extract parameter information from page"""
        parameters = []

        # Look for parameter tables or lists
        param_tables = soup.find_all('table')
        for table in param_tables:
            rows = table.find_all('tr')
            for row in rows[1:]:  # Skip header
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    param_name = cells[0].get_text().strip()
                    param_desc = cells[1].get_text().strip()
                    if param_name and param_desc:
                        parameters.append({
                            'name': param_name,
                            'description': param_desc
                        })

        return parameters

    def _extract_examples(self, soup: BeautifulSoup) -> List[str]:
        """Extract code examples from page"""
        examples = []

        # Look for code blocks
        code_blocks = soup.find_all(['pre', 'code'])
        for block in code_blocks:
            code_text = block.get_text().strip()
            if len(code_text) > 20:  # Filter out small snippets
                examples.append(code_text[:1000])  # Limit size

        return examples

class DataForSEODocumentationRAG:
    """RAG system for DataForSEO API documentation"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model_name = model_name
        self.model = None
        self.documents = []
        self.embeddings = None
        self.index_file = "dataforseo_doc_index.pkl"
        
        if EMBEDDINGS_AVAILABLE:
            self.model = SentenceTransformer(model_name)
            self._load_or_create_index()
        else:
            logger.warning("Sentence transformers not available. Using fallback search.")
    
    def _load_or_create_index(self):
        """Load existing index or create new one"""
        if os.path.exists(self.index_file):
            try:
                with open(self.index_file, 'rb') as f:
                    data = pickle.load(f)
                    self.documents = data['documents']
                    self.embeddings = data['embeddings']
                logger.info(f"Loaded existing documentation index with {len(self.documents)} documents")
                return
            except Exception as e:
                logger.warning(f"Failed to load index: {e}. Creating new one.")
        
        self._create_documentation_index()
    
    def _create_documentation_index(self):
        """Create semantic search index from DataForSEO documentation"""
        logger.info("Creating documentation index from live DataForSEO docs...")

        # Use the crawler to get fresh documentation
        crawler = DataForSEODocumentationCrawler()
        crawled_docs = crawler.crawl_documentation()

        self.documents = []

        # Process crawled documents
        for doc in crawled_docs:
            processed_doc = {
                "endpoint": doc.get("endpoint", ""),
                "category": doc.get("category", ""),
                "title": doc.get("title", ""),
                "description": doc.get("description", ""),
                "parameters": doc.get("parameters", []),
                "examples": doc.get("examples", []),
                "url": doc.get("url", ""),
                "source": doc.get("source", ""),
                "search_text": self._create_search_text_from_crawled(doc)
            }
            self.documents.append(processed_doc)

        # Also include some static high-quality documentation for key endpoints
        self._add_static_documentation()
        
        # Create embeddings for all documents
        if self.model:
            search_texts = [doc["search_text"] for doc in self.documents]
            self.embeddings = self.model.encode(search_texts)
            
            # Save index for future use
            try:
                with open(self.index_file, 'wb') as f:
                    pickle.dump({
                        'documents': self.documents,
                        'embeddings': self.embeddings
                    }, f)
                logger.info(f"Created and saved documentation index with {len(self.documents)} documents")
            except Exception as e:
                logger.warning(f"Failed to save index: {e}")
    
    def _create_search_text(self, endpoint: str, endpoint_data: Dict, category_desc: str) -> str:
        """Create comprehensive search text for an endpoint"""
        parts = [
            endpoint,
            endpoint_data["description"],
            category_desc,
            " ".join(endpoint_data["use_cases"]),
            " ".join(endpoint_data["required_params"]),
            " ".join(endpoint_data.get("optional_params", [])),
        ]
        
        # Add example parameters as searchable text
        if "example" in endpoint_data:
            example_text = " ".join([f"{k} {v}" for k, v in endpoint_data["example"].items()])
            parts.append(example_text)
        
        return " ".join(parts).lower()

    def _create_search_text_from_crawled(self, doc: Dict[str, Any]) -> str:
        """Create comprehensive search text from crawled document"""
        parts = [
            doc.get("endpoint", ""),
            doc.get("title", ""),
            doc.get("description", ""),
            doc.get("category", ""),
        ]

        # Add parameter names and descriptions
        for param in doc.get("parameters", []):
            if isinstance(param, dict):
                parts.append(param.get("name", ""))
                parts.append(param.get("description", ""))

        # Add examples
        for example in doc.get("examples", []):
            if isinstance(example, str):
                parts.append(example[:200])  # Limit example text

        # Add content snippet
        content = doc.get("content", "")
        if content:
            parts.append(content[:300])

        return " ".join(str(part) for part in parts if part).lower()

    def _add_static_documentation(self):
        """Add high-quality static documentation for key endpoints"""
        static_docs = [
            {
                "endpoint": "/serp/google/organic/live/advanced",
                "category": "SERP API",
                "title": "Live Google Organic SERP Advanced",
                "description": "Get real-time Google organic search results with full SERP features including featured snippets, knowledge graphs, and more. Returns top 100 results for specified keyword, location, and language.",
                "parameters": [
                    {"name": "keyword", "description": "Search keyword (required)"},
                    {"name": "location_code", "description": "Geographic location code (default: 2840 for USA)"},
                    {"name": "language_code", "description": "Language code (default: 'en')"},
                    {"name": "device", "description": "Device type: desktop, mobile, tablet"},
                    {"name": "os", "description": "Operating system: windows, macos, linux, android, ios"}
                ],
                "examples": ['{"keyword": "digital marketing", "location_code": 2840, "language_code": "en"}'],
                "url": "https://docs.dataforseo.com/v3/serp/google/organic/live/advanced/",
                "source": "static_documentation",
                "search_text": "serp google organic live advanced search results keyword location language device real-time SERP features"
            },
            {
                "endpoint": "/dataforseo_labs/google/related_keywords/live",
                "category": "DataForSEO Labs",
                "title": "Related Keywords",
                "description": "Get semantically related keywords with SEO metrics including search volume, keyword difficulty, and competition data.",
                "parameters": [
                    {"name": "keyword", "description": "Seed keyword for finding related terms (required)"},
                    {"name": "location_code", "description": "Geographic location code"},
                    {"name": "language_code", "description": "Language code"},
                    {"name": "limit", "description": "Number of related keywords to return (max 1000)"}
                ],
                "examples": ['{"keyword": "content marketing", "location_code": 2840, "limit": 50}'],
                "url": "https://docs.dataforseo.com/v3/dataforseo_labs/google/related_keywords/live/",
                "source": "static_documentation",
                "search_text": "related keywords semantic keyword research expansion content planning SEO metrics"
            },
            {
                "endpoint": "/dataforseo_labs/google/keyword_ideas/live",
                "category": "DataForSEO Labs",
                "title": "Keyword Ideas",
                "description": "Generate comprehensive keyword ideas for large-scale keyword research with search volume, competition, and trend data.",
                "parameters": [
                    {"name": "keyword", "description": "Seed keyword for generating ideas (required)"},
                    {"name": "location_code", "description": "Geographic location code"},
                    {"name": "language_code", "description": "Language code"},
                    {"name": "limit", "description": "Number of keyword ideas to return (max 1000)"}
                ],
                "examples": ['{"keyword": "digital marketing", "location_code": 2840, "limit": 1000}'],
                "url": "https://docs.dataforseo.com/v3/dataforseo_labs/google/keyword_ideas/live/",
                "source": "static_documentation",
                "search_text": "keyword ideas generation research discovery content strategy PPC planning"
            }
        ]

        for doc in static_docs:
            self.documents.append(doc)

    def semantic_search(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Perform semantic search over documentation"""
        if not EMBEDDINGS_AVAILABLE or self.model is None:
            return self._fallback_search(query, top_k)
        
        # Encode the query
        query_embedding = self.model.encode([query.lower()])
        
        # Calculate similarities
        similarities = np.dot(query_embedding, self.embeddings.T)[0]
        
        # Get top-k most similar documents
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            doc = self.documents[idx].copy()
            doc["similarity_score"] = float(similarities[idx])
            results.append(doc)
        
        return results
    
    def _fallback_search(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Fallback keyword-based search when embeddings not available"""
        query_words = set(query.lower().split())
        
        scored_docs = []
        for doc in self.documents:
            search_words = set(doc["search_text"].split())
            overlap = len(query_words.intersection(search_words))
            if overlap > 0:
                score = overlap / len(query_words.union(search_words))
                doc_copy = doc.copy()
                doc_copy["similarity_score"] = score
                scored_docs.append(doc_copy)
        
        # Sort by score and return top-k
        scored_docs.sort(key=lambda x: x["similarity_score"], reverse=True)
        return scored_docs[:top_k]
    
    def find_best_endpoint(self, user_query: str) -> Dict[str, Any]:
        """Find the best endpoint for a user query using semantic search"""
        results = self.semantic_search(user_query, top_k=1)

        if not results:
            return {
                "error": "No matching endpoint found",
                "suggestion": "Please be more specific about your SEO data needs"
            }

        best_match = results[0]

        # Extract parameters from the parameters list if available
        required_params = []
        optional_params = []

        for param in best_match.get("parameters", []):
            if isinstance(param, dict):
                param_name = param.get("name", "")
                if param_name:
                    if "required" in param.get("description", "").lower():
                        required_params.append(param_name)
                    else:
                        optional_params.append(param_name)

        # If no parameters found, use common defaults based on endpoint type
        if not required_params and not optional_params:
            endpoint = best_match.get("endpoint", "")
            if "/serp/" in endpoint:
                required_params = ["keyword"]
                optional_params = ["location_code", "language_code", "device"]
            elif "/dataforseo_labs/" in endpoint:
                if "keyword" in endpoint:
                    required_params = ["keyword"]
                elif "domain" in endpoint or "ranked" in endpoint:
                    required_params = ["target"]
                optional_params = ["location_code", "language_code", "limit"]
            elif "/backlinks/" in endpoint:
                required_params = ["target"]
                optional_params = ["limit"]
            elif "/keywords_data/" in endpoint:
                required_params = ["keywords"]
                optional_params = ["location_code", "language_code"]

        return {
            "endpoint": best_match.get("endpoint", ""),
            "category": best_match.get("category", ""),
            "confidence": best_match.get("similarity_score", 0),
            "description": best_match.get("description", ""),
            "title": best_match.get("title", ""),
            "required_params": required_params,
            "optional_params": optional_params,
            "use_cases": best_match.get("use_cases", []),
            "example": best_match.get("examples", []),
            "url": best_match.get("url", ""),
            "reasoning": f"Semantic match (confidence: {best_match.get('similarity_score', 0):.3f})"
        }
    
    def get_endpoint_suggestions(self, user_query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Get multiple endpoint suggestions for a user query"""
        return self.semantic_search(user_query, top_k=top_k)

# Global RAG system instance
_rag_system = None

def get_rag_system() -> DataForSEODocumentationRAG:
    """Get or create the global RAG system instance"""
    global _rag_system
    if _rag_system is None:
        _rag_system = DataForSEODocumentationRAG()
    return _rag_system

@tool
def search_dataforseo_documentation(
    search_query: str,
    max_results: int = 3
) -> Dict[str, Any]:
    """
    Semantic search over DataForSEO API documentation to find relevant endpoints and parameters.
    This tool uses RAG (Retrieval-Augmented Generation) to prevent LLM hallucination by providing
    accurate, contextual information about available DataForSEO API endpoints.
    
    Args:
        search_query: Natural language description of what SEO data or analysis you need
        max_results: Maximum number of endpoint suggestions to return (default: 3)
        
    Returns:
        Dict containing:
        - best_match: The most relevant endpoint with full documentation
        - alternatives: Other relevant endpoints to consider
        - search_metadata: Information about the search process
        
    Examples:
        search_query="find related keywords for content marketing"
        search_query="get search volume data for multiple keywords"
        search_query="analyze competitor domain rankings"
        search_query="check backlink profile of a website"
        search_query="get Google Maps local search results"
    """
    rag_system = get_rag_system()
    
    # Get the best match
    best_match = rag_system.find_best_endpoint(search_query)
    
    # Get alternative suggestions
    all_suggestions = rag_system.get_endpoint_suggestions(search_query, max_results)
    alternatives = all_suggestions[1:] if len(all_suggestions) > 1 else []
    
    return {
        "search_query": search_query,
        "best_match": best_match,
        "alternatives": [
            {
                "endpoint": alt.get("endpoint", ""),
                "description": alt.get("description", ""),
                "confidence": alt.get("similarity_score", 0),
                "use_cases": alt.get("use_cases", [])
            }
            for alt in alternatives
        ],
        "search_metadata": {
            "total_documents_searched": len(rag_system.documents),
            "embedding_model": rag_system.model_name if EMBEDDINGS_AVAILABLE else "keyword_fallback",
            "results_found": len(all_suggestions)
        }
    }

@tool  
def get_endpoint_documentation(
    endpoint_path: str
) -> Dict[str, Any]:
    """
    Get comprehensive documentation for a specific DataForSEO API endpoint.
    
    Args:
        endpoint_path: The DataForSEO API endpoint path (e.g., '/serp/google/organic/live/advanced')
        
    Returns:
        Dict containing complete endpoint documentation including parameters, examples, and use cases
    """
    rag_system = get_rag_system()
    
    # Find the document for this endpoint
    for doc in rag_system.documents:
        if doc.get("endpoint") == endpoint_path:
            # Extract parameters
            required_params = []
            optional_params = []

            for param in doc.get("parameters", []):
                if isinstance(param, dict):
                    param_name = param.get("name", "")
                    if param_name:
                        if "required" in param.get("description", "").lower():
                            required_params.append(param_name)
                        else:
                            optional_params.append(param_name)

            return {
                "endpoint": doc.get("endpoint", ""),
                "category": doc.get("category", ""),
                "title": doc.get("title", ""),
                "description": doc.get("description", ""),
                "required_parameters": required_params,
                "optional_parameters": optional_params,
                "use_cases": doc.get("use_cases", []),
                "example_request": doc.get("examples", []),
                "url": doc.get("url", ""),
                "source": doc.get("source", "")
            }
    
    return {
        "error": f"Endpoint '{endpoint_path}' not found in documentation",
        "suggestion": "Use search_dataforseo_documentation to find available endpoints"
    }
