#!/usr/bin/env python3
"""
Test script for refactored DataForSEO client with RAG integration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.direct_dataforseo_client import (
    intelligent_dataforseo_query,
    rag_dataforseo_query,
    call_dataforseo_api_endpoint
)

def test_refactored_client():
    """Test the refactored DataForSEO client"""
    print("Testing Refactored DataForSEO Client with RAG Integration...")
    
    # Test 1: Simple RAG query
    print("\n1. Testing simple RAG query...")
    try:
        result = rag_dataforseo_query(
            user_query="find related keywords for content marketing",
            target_keyword="content marketing",
            result_limit=5
        )
        
        if 'error' in result:
            print(f"   ❌ RAG query failed: {result['error']}")
        else:
            print(f"   ✅ RAG query successful")
            print(f"   Endpoint: {result.get('endpoint_info', {}).get('endpoint', 'N/A')}")
            print(f"   Confidence: {result.get('endpoint_info', {}).get('confidence', 0):.3f}")
            
            # Check if API response is present
            api_response = result.get('api_response', {})
            if 'error' in api_response:
                print(f"   ⚠️  API call failed: {api_response['error']}")
            else:
                print(f"   ✅ API call successful")
                
    except Exception as e:
        print(f"   ❌ RAG query exception: {e}")
    
    # Test 2: Intelligent query
    print("\n2. Testing intelligent DataForSEO query...")
    try:
        result = intelligent_dataforseo_query(
            natural_language_query="get search volume data for digital marketing keywords",
            target_keywords=["digital marketing", "seo", "content marketing"],
            result_limit=10
        )
        
        if 'error' in result:
            print(f"   ❌ Intelligent query failed: {result['error']}")
        else:
            print(f"   ✅ Intelligent query successful")
            endpoint_info = result.get('endpoint_info', {})
            print(f"   Endpoint: {endpoint_info.get('selected_endpoint', 'N/A')}")
            print(f"   Confidence: {endpoint_info.get('confidence_score', 0):.3f}")
            print(f"   Reasoning: {endpoint_info.get('selection_reasoning', 'N/A')}")
            
            # Check documentation search
            doc_search = result.get('documentation_search', {})
            search_meta = doc_search.get('search_metadata', {})
            print(f"   Documents searched: {search_meta.get('total_documents_searched', 0)}")
            print(f"   Embedding model: {search_meta.get('embedding_model', 'N/A')}")
            
    except Exception as e:
        print(f"   ❌ Intelligent query exception: {e}")
    
    # Test 3: Direct API endpoint call
    print("\n3. Testing direct API endpoint call...")
    try:
        result = call_dataforseo_api_endpoint(
            api_endpoint_path="/dataforseo_labs/google/related_keywords/live",
            keyword="seo tools",
            location_code=2840,
            language_code="en",
            search_limit=5
        )
        
        if 'error' in result:
            print(f"   ❌ Direct API call failed: {result['error']}")
        else:
            print(f"   ✅ Direct API call successful")
            
            # Check response structure
            if 'tasks' in result:
                tasks = result.get('tasks', [])
                if tasks and len(tasks) > 0:
                    task = tasks[0]
                    print(f"   Task status: {task.get('status_message', 'N/A')}")
                    
                    if 'result' in task and task['result']:
                        results = task['result']
                        print(f"   Results count: {len(results)}")
                        if results and len(results) > 0:
                            first_result = results[0]
                            if 'items' in first_result:
                                items = first_result['items']
                                print(f"   Keywords found: {len(items)}")
                            
    except Exception as e:
        print(f"   ❌ Direct API call exception: {e}")
    
    # Test 4: Test with domain analysis query
    print("\n4. Testing domain analysis query...")
    try:
        result = rag_dataforseo_query(
            user_query="analyze what keywords semrush.com ranks for",
            target_domain="semrush.com",
            result_limit=10
        )
        
        if 'error' in result:
            print(f"   ❌ Domain analysis failed: {result['error']}")
        else:
            print(f"   ✅ Domain analysis successful")
            endpoint_info = result.get('endpoint_info', {})
            print(f"   Endpoint: {endpoint_info.get('endpoint', 'N/A')}")
            print(f"   Description: {endpoint_info.get('description', 'N/A')[:100]}...")
            
    except Exception as e:
        print(f"   ❌ Domain analysis exception: {e}")
    
    print("\n✅ Refactored client testing complete!")

if __name__ == "__main__":
    test_refactored_client()
