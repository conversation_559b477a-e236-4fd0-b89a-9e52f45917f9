#!/usr/bin/env python3
"""
Direct DataForSEO API Client
A simple, working client that calls DataForSEO API directly
This bypasses MCP complexity while maintaining the LLM-driven tool selection approach
"""

import os
import requests
import json
import base64
from typing import Dict, Any, List
from smolagents import tool
import logging

# Try to load .env file if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

logger = logging.getLogger(__name__)

class DirectDataForSEOClient:
    """Direct client for DataForSEO API"""
    
    def __init__(self, username: str = None, password: str = None):
        # Support both LOGIN and USERNAME formats
        self.username = username or os.getenv('DATAFORSEO_LOGIN') or os.getenv('DATAFORSEO_USERNAME')
        self.password = password or os.getenv('DATAFORSEO_PASSWORD')
        
        if not self.username or not self.password:
            logger.warning("DataForSEO credentials not provided. Set DATAFORSEO_USERNAME/DATAFORSEO_PASSWORD")
            return
        
        # Create auth header
        credentials = f"{self.username}:{self.password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        
        self.headers = {
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/json'
        }
        
        self.base_url = 'https://api.dataforseo.com/v3'
        
        logger.info("DataForSEO client initialized successfully")
    
    def _make_request(self, endpoint: str, data: List[Dict]) -> Dict[str, Any]:
        """Make a request to DataForSEO API"""
        if not self.username or not self.password:
            return {
                "error": "DataForSEO credentials not configured",
                "status": "error"
            }
        
        try:
            url = f"{self.base_url}{endpoint}"
            response = requests.post(url, headers=self.headers, json=data)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "error": f"API request failed with status {response.status_code}",
                    "details": response.text,
                    "status": "error"
                }
                
        except Exception as e:
            return {
                "error": f"Request failed: {str(e)}",
                "status": "error"
            }

# Global client instance
_client = None

def get_dataforseo_client() -> DirectDataForSEOClient:
    """Get or create the global DataForSEO client"""
    global _client
    if _client is None:
        _client = DirectDataForSEOClient()
    return _client

# LLM-callable tools using direct API calls
@tool
def serp_google_organic_live(keyword: str, location_name: str = "United States", language_code: str = "en", device: str = "desktop") -> Dict[str, Any]:
    """
    Get live Google organic search results for a keyword

    Args:
        keyword: The search keyword
        location_name: Location name (default: "United States")
        language_code: Language code (default: "en")
        device: Device type (default: "desktop")

    Returns:
        Dict containing SERP results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_name": location_name,
        "language_code": language_code,
        "device": device,
        "os": "windows"
    }]

    return client._make_request('/serp/google/organic/live/advanced', data)

@tool
def labs_google_related_keywords(keyword: str, location_name: str = "United States", language_code: str = "en", limit: int = 10) -> Dict[str, Any]:
    """
    Get related keywords for a given keyword using DataForSEO Labs

    Args:
        keyword: The seed keyword
        location_name: Location name (default: "United States")
        language_code: Language code (default: "en")
        limit: Number of related keywords to return (default: 10)

    Returns:
        Dict containing related keywords with metrics
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_name": location_name,
        "language_code": language_code,
        "limit": limit
    }]

    return client._make_request('/dataforseo_labs/google/related_keywords/live', data)

@tool
def labs_google_keyword_ideas(keyword: str, location_name: str = "United States", language_code: str = "en", limit: int = 100) -> Dict[str, Any]:
    """
    Get keyword ideas for comprehensive keyword research

    Args:
        keyword: The seed keyword
        location_name: Location name (default: "United States")
        language_code: Language code (default: "en")
        limit: Number of keyword ideas to return (default: 100)

    Returns:
        Dict containing keyword ideas with metrics
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_name": location_name,
        "language_code": language_code,
        "limit": limit
    }]

    return client._make_request('/dataforseo_labs/google/keyword_ideas/live', data)

@tool
def backlinks_summary(target: str, limit: int = 100) -> Dict[str, Any]:
    """
    Get backlinks summary for a domain
    
    Args:
        target: The target domain to analyze
        limit: Number of backlinks to include in summary (default: 100)
        
    Returns:
        Dict containing backlinks summary
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "limit": limit
    }]
    
    return client._make_request('/backlinks/summary/live', data)

@tool
def keywords_google_ads_search_volume(keywords: List[str], location_name: str = "United States", language_code: str = "en") -> Dict[str, Any]:
    """
    Get search volume data for keywords using Google Ads API

    Args:
        keywords: List of keywords to analyze
        location_name: Location name (default: "United States")
        language_code: Language code (default: "en")

    Returns:
        Dict containing search volume data
    """
    client = get_dataforseo_client()
    data = [{
        "keywords": keywords,
        "location_name": location_name,
        "language_code": language_code
    }]

    return client._make_request('/keywords_data/google_ads/search_volume/live', data)

@tool
def labs_google_domain_rank_overview(target: str, location_name: str = "United States", language_code: str = "en") -> Dict[str, Any]:
    """
    Get domain ranking overview and metrics

    Args:
        target: The target domain to analyze
        location_name: Location name (default: "United States")
        language_code: Language code (default: "en")

    Returns:
        Dict containing domain ranking overview
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "location_name": location_name,
        "language_code": language_code
    }]

    return client._make_request('/dataforseo_labs/google/domain_rank_overview/live', data)

@tool
def labs_google_ranked_keywords(target: str, location_name: str = "United States", language_code: str = "en", limit: int = 100) -> Dict[str, Any]:
    """
    Get keywords that a domain ranks for (competitor analysis)

    Args:
        target: The target domain to analyze
        location_name: Location name (default: "United States")
        language_code: Language code (default: "en")
        limit: Number of keywords to return (default: 100)

    Returns:
        Dict containing keywords the domain ranks for
    """
    client = get_dataforseo_client()
    data = [{
        "target": target,
        "location_name": location_name,
        "language_code": language_code,
        "limit": limit
    }]

    return client._make_request('/dataforseo_labs/google/ranked_keywords/live', data)

# ============================================================================
# FLEXIBLE DATAFORSEO API CALLER - LLM can call ANY endpoint
# ============================================================================

@tool
def call_dataforseo_api_endpoint(
    api_endpoint_path: str,
    keyword: str = None,
    keywords: List[str] = None,
    target_domain: str = None,
    target_url: str = None,
    location_name: str = "United States",
    language_code: str = "en",
    device_type: str = "desktop",
    operating_system: str = "windows",
    search_limit: int = 100,
    additional_parameters: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Call any DataForSEO API endpoint with comprehensive parameter support. This flexible tool allows access to 100+ DataForSEO API endpoints for SERP analysis, keyword research, backlink analysis, on-page SEO, domain analytics, and more. Documentation is here:
    https://docs.dataforseo.com/v3/

    Args:
        api_endpoint_path: DataForSEO API endpoint (e.g., '/serp/google/organic/live/advanced',
                          '/dataforseo_labs/google/related_keywords/live', '/backlinks/summary/live')
        keyword: Single keyword for SERP analysis or keyword research (e.g., 'digital marketing')
        keywords: List of keywords for bulk operations (e.g., ['seo', 'marketing', 'analytics'])
        target_domain: Domain to analyze (e.g., 'semrush.com', 'ahrefs.com')
        target_url: Specific URL to analyze (e.g., 'https://example.com/page')
        location_name: Geographic location name (e.g., 'United States', 'United Kingdom', 'Germany', 'New York')
        language_code: Language code (en=English, es=Spanish, fr=French, de=German)
        device_type: Device for SERP analysis (desktop, mobile, tablet)
        operating_system: OS for SERP analysis (windows, macos, linux, android, ios)
        search_limit: Maximum number of results to return (1-1000, default 100)
        additional_parameters: Any other endpoint-specific parameters as a dictionary

    Returns:
        Dict containing the complete API response with status, tasks, and results

    Common Endpoints:
        SERP Analysis:
        - '/serp/google/organic/live/advanced' - Google search results
        - '/serp/google/maps/live/advanced' - Google Maps results
        - '/serp/bing/organic/live/advanced' - Bing search results

        Keyword Research:
        - '/dataforseo_labs/google/related_keywords/live' - Related keywords with metrics
        - '/dataforseo_labs/google/keyword_ideas/live' - Keyword suggestions
        - '/dataforseo_labs/google/bulk_keyword_difficulty/live' - Keyword difficulty scores
        - '/keywords_data/google_ads/search_volume/live' - Search volume data

        Competitor Analysis:
        - '/dataforseo_labs/google/ranked_keywords/live' - Keywords a domain ranks for
        - '/dataforseo_labs/google/domain_rank_overview/live' - Domain ranking metrics

        Backlink Analysis:
        - '/backlinks/summary/live' - Backlink summary for domain
        - '/backlinks/anchors/live' - Anchor text analysis
        - '/backlinks/referring_domains/live' - Referring domains

        On-Page SEO:
        - '/on_page/summary/live' - On-page SEO summary
        - '/on_page/pages/live' - Page-level analysis
        - '/on_page/lighthouse/live' - Google Lighthouse scores

        Domain Analytics:
        - '/domain_analytics/google/overview/live' - Domain overview
        - '/domain_analytics/google/technologies/live' - Technologies used
        - '/domain_analytics/google/whois/live' - WHOIS information
    """
    client = get_dataforseo_client()

    # Ensure endpoint starts with /
    if not api_endpoint_path.startswith('/'):
        api_endpoint_path = '/' + api_endpoint_path

    # Build parameters dictionary based on provided arguments
    parameters = {}

    # Add keyword parameters
    if keyword is not None:
        parameters['keyword'] = keyword
    if keywords is not None:
        parameters['keywords'] = keywords

    # Add target parameters
    if target_domain is not None:
        parameters['target'] = target_domain
    if target_url is not None:
        parameters['target'] = target_url

    # Add location and language (use location_name instead of location_code)
    parameters['location_name'] = location_name
    parameters['language_code'] = language_code

    # Add device and OS for SERP endpoints
    if 'serp' in api_endpoint_path:
        parameters['device'] = device_type
        parameters['os'] = operating_system

    # Add limit for endpoints that support it
    if any(endpoint_type in api_endpoint_path for endpoint_type in ['related_keywords', 'keyword_ideas', 'ranked_keywords']):
        parameters['limit'] = search_limit

    # Add any additional parameters
    if additional_parameters:
        parameters.update(additional_parameters)

    # Create data payload - DataForSEO endpoints expect an array with one object
    data = [parameters]

    return client._make_request(api_endpoint_path, data)

# ============================================================================
# RAG-POWERED DATAFORSEO CALLER - Advanced Semantic Documentation Search
# ============================================================================

@tool
def intelligent_dataforseo_query(
    natural_language_query: str,
    target_keyword: str = None,
    target_keywords: List[str] = None,
    target_domain: str = None,
    target_url: str = None,
    geographic_location: str = "United States",
    language_preference: str = "English",
    result_limit: int = 100,
    device_preference: str = "desktop"
) -> Dict[str, Any]:
    """
    Advanced DataForSEO query tool powered by RAG (Retrieval-Augmented Generation).
    Uses semantic search over comprehensive API documentation to intelligently select
    the best endpoint and parameters, preventing LLM hallucination.

    Args:
        natural_language_query: Describe what SEO data you need in natural language
                               (e.g., "find semantically related keywords for content marketing",
                                "analyze competitor rankings for digital marketing tools",
                                "get search volume data for my keyword list")
        target_keyword: Single keyword for analysis (e.g., "digital marketing")
        target_keywords: List of keywords for bulk operations (e.g., ["seo", "marketing", "analytics"])
        target_domain: Domain to analyze (e.g., "semrush.com", "ahrefs.com")
        target_url: Specific URL to analyze (e.g., "https://example.com/blog/seo-guide")
        geographic_location: Location for geo-targeted analysis ("United States", "United Kingdom", "Germany", "New York")
        language_preference: Language for analysis ("English", "Spanish", "French", "German")
        result_limit: Maximum number of results to return (1-1000)
        device_preference: Device type for SERP analysis ("desktop", "mobile", "tablet")

    Returns:
        Dict containing:
        - documentation_search: RAG search results and endpoint selection reasoning
        - api_response: The actual DataForSEO API response
        - endpoint_info: Details about the selected endpoint and parameters used
        - confidence_score: Semantic similarity confidence for the endpoint selection

    This tool combines:
        1. Semantic search over DataForSEO documentation
        2. Intelligent parameter mapping and validation
        3. Automatic endpoint selection based on user intent
        4. Real-time API calls with proper error handling
    """
    try:
        from .dataforseo_rag_system import search_dataforseo_documentation

        # Step 1: Use RAG to find the best endpoint
        doc_search_result = search_dataforseo_documentation(
            search_query=natural_language_query,
            max_results=3
        )

        best_match = doc_search_result.get('best_match', {})
        if 'error' in best_match:
            return {
                "error": "Could not find suitable DataForSEO endpoint",
                "documentation_search": doc_search_result,
                "suggestion": "Please refine your query or check available endpoints"
            }

        # Extract endpoint information safely
        endpoint = best_match.get('endpoint', '')
        required_params = best_match.get('required_params', [])

        if not endpoint:
            return {
                "error": "No valid endpoint found",
                "documentation_search": doc_search_result,
                "suggestion": "Please try a different query"
            }

        # Step 2: Map user inputs to API parameters
        # Use location_name instead of location_code (DataForSEO supports both)
        # This is more user-friendly and readable
        location_name = geographic_location  # Use the full location name directly

        # Language mapping (simplified common mappings)
        language_mapping = {
            "english": "en", "spanish": "es", "french": "fr",
            "german": "de", "italian": "it", "portuguese": "pt",
            "russian": "ru", "chinese": "zh", "japanese": "ja"
        }

        language_code = "en"  # Default to English
        for lang_name, code in language_mapping.items():
            if lang_name in language_preference.lower():
                language_code = code
                break

        # Step 3: Build parameters based on endpoint requirements
        parameters = {
            "location_name": location_name,  # Use location_name instead of location_code
            "language_code": language_code
        }

        # Handle required parameters
        if "keyword" in required_params:
            if target_keyword:
                parameters["keyword"] = target_keyword
            elif target_keywords and len(target_keywords) > 0:
                parameters["keyword"] = target_keywords[0]
            else:
                # Try to extract keyword from natural language query
                import re
                keyword_match = re.search(r"['\"]([^'\"]+)['\"]", natural_language_query)
                if keyword_match:
                    parameters["keyword"] = keyword_match.group(1)
                else:
                    for_match = re.search(r"\bfor\s+([a-zA-Z0-9\s]+?)(?:\s+in\s|\s+on\s|\s*$)", natural_language_query)
                    if for_match:
                        parameters["keyword"] = for_match.group(1).strip()
                    else:
                        return {
                            "error": "Keyword required but not provided or extractable from query",
                            "suggestion": "Please provide target_keyword parameter or include keyword in quotes"
                        }

        if "keywords" in required_params:
            if target_keywords:
                parameters["keywords"] = target_keywords
            elif target_keyword:
                parameters["keywords"] = [target_keyword]
            else:
                return {
                    "error": "Keywords list required but not provided",
                    "suggestion": "Please provide target_keywords parameter"
                }

        if "target" in required_params:
            if target_domain:
                parameters["target"] = target_domain
            elif target_url:
                parameters["target"] = target_url
            else:
                import re
                domain_match = re.search(r"([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})", natural_language_query)
                if domain_match:
                    parameters["target"] = domain_match.group(1)
                else:
                    return {
                        "error": "Target domain/URL required but not provided or extractable from query",
                        "suggestion": "Please provide target_domain or target_url parameter"
                    }

        # Add endpoint-specific optional parameters
        optional_params = best_match.get("optional_params", [])
        if "limit" in optional_params:
            parameters["limit"] = result_limit
        if "device" in optional_params:
            parameters["device"] = device_preference
        if "os" in optional_params:
            parameters["os"] = "windows"

        # Step 4: Make the API call
        client = get_dataforseo_client()
        api_response = client._make_request(endpoint, [parameters])

        return {
            "documentation_search": doc_search_result,
            "endpoint_info": {
                "selected_endpoint": endpoint,
                "confidence_score": best_match.get("confidence", 0),
                "selection_reasoning": best_match.get("reasoning", ""),
                "parameters_used": parameters,
                "endpoint_description": best_match.get("description", ""),
                "use_cases": best_match.get("use_cases", [])
            },
            "api_response": api_response,
            "query_metadata": {
                "original_query": natural_language_query,
                "geographic_location": geographic_location,
                "language_preference": language_preference,
                "embedding_model_used": doc_search_result.get("search_metadata", {}).get("embedding_model", "unknown")
            }
        }

    except Exception as e:
        return {
            "error": f"Failed to process intelligent query: {str(e)}",
            "suggestion": "Please try using the basic call_dataforseo_api_endpoint function instead"
        }

# ============================================================================
# SIMPLE RAG-POWERED DATAFORSEO CALLER - Clean Implementation
# ============================================================================

@tool
def rag_dataforseo_query(
    user_query: str,
    target_keyword: str = None,
    target_keywords: List[str] = None,
    target_domain: str = None,
    location_name: str = "United States",
    language: str = "English",
    result_limit: int = 100
) -> Dict[str, Any]:
    """
    Simple RAG-powered DataForSEO query tool that uses semantic search to find the best endpoint.

    Args:
        user_query: Natural language description of what SEO data you need
        target_keyword: Single keyword for analysis
        target_keywords: List of keywords for bulk analysis
        target_domain: Domain to analyze
        location_name: Geographic location for analysis
        language: Language for analysis
        result_limit: Maximum number of results to return

    Returns:
        Dict containing API response and endpoint information
    """
    try:
        from .dataforseo_rag_system import get_rag_system

        # Get RAG system and find best endpoint
        rag = get_rag_system()
        endpoint_result = rag.find_best_endpoint(user_query)

        if 'error' in endpoint_result:
            return endpoint_result

        endpoint = endpoint_result.get('endpoint', '')
        if not endpoint:
            return {"error": "No valid endpoint found"}

        # Build parameters using location_name instead of location_code
        parameters = {
            "location_name": location_name,  # Use full location name
            "language_code": "en"   # Default English
        }

        # Handle required parameters based on endpoint type
        if 'keyword' in endpoint and target_keyword:
            parameters["keyword"] = target_keyword
        elif 'keywords' in endpoint and target_keywords:
            parameters["keywords"] = target_keywords
        elif 'target' in endpoint and target_domain:
            parameters["target"] = target_domain

        # Add limit for applicable endpoints
        if any(x in endpoint for x in ['related_keywords', 'keyword_ideas', 'ranked_keywords']):
            parameters["limit"] = result_limit

        # Make API call
        client = get_dataforseo_client()
        api_response = client._make_request(endpoint, [parameters])

        return {
            "endpoint_info": endpoint_result,
            "api_response": api_response,
            "parameters_used": parameters
        }

    except Exception as e:
        return {
            "error": f"RAG query failed: {str(e)}",
            "suggestion": "Please try using the basic call_dataforseo_api_endpoint function"
        }

# ============================================================================
# SERP API - Additional Search Engines and Types
# ============================================================================

@tool
def serp_google_maps_live(keyword: str, location_name: str = "United States", language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Maps search results

    Args:
        keyword: The search keyword
        location_name: Location name (default: "United States")
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Maps results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_name": location_name,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/maps/live/advanced', data)

@tool
def serp_google_images_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google Images search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google Images results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/images/live/advanced', data)

@tool
def serp_google_news_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Google News search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Google News results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/google/news/live/advanced', data)

@tool
def serp_bing_organic_live(keyword: str, location_code: int = 2840, language_code: str = "en") -> Dict[str, Any]:
    """
    Get live Bing organic search results

    Args:
        keyword: The search keyword
        location_code: Location code (default: 2840 for USA)
        language_code: Language code (default: "en")

    Returns:
        Dict containing Bing search results
    """
    client = get_dataforseo_client()
    data = [{
        "keyword": keyword,
        "location_code": location_code,
        "language_code": language_code
    }]

    return client._make_request('/serp/bing/organic/live/advanced', data)

# End of file
