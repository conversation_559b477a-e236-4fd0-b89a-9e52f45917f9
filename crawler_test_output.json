{"total_documents": 724, "sample_documents": [{"endpoint": "/serp/overview", "title": "SERP API: Overview", "category": "SERP API", "description": "- [SERP API ID List](https://docs.dataforseo.com/v3/serp/id_list.md) — This endpoint is designed to provide you with the list of IDs and metadata of the completed SERP tasks during the specified period. You will get all task IDs that were made including successful, uncompleted, and tasks that responded as errors. - [SERP API Errors](https://docs.dataforseo.com/v3/serp/errors.md) — By calling this endpoint you will receive information about the SERP API tasks that returned an error within the past 7 days. - [SERP API Page Screenshot](https://docs.dataforseo.com/v3/serp/screenshot.md) — Using the Live Page Screenshot endpoint, you can capture a screenshot of any SERP page.", "url": "https://docs.dataforseo.com/v3/serp/overview.md", "content": "\n- [SERP API: Overview](https://docs.dataforseo.com/v3/serp/overview.md) — SERP API encompasses a broad range of endpoints. You can get the full list [here](https://docs.dataforseo.com/v3/serp/endpoints.md).\n- [SERP API ID List](https://docs.dataforseo.com/v3/serp/id_list.md) — This endpoint is designed to provide you with the list of IDs and metadata of the completed SERP tasks during the specified period. You will get all task IDs that were made including successful, uncompleted, and tasks that responded as errors.\n- [SERP API Errors](https://docs.dataforseo.com/v3/serp/errors.md) — By calling this endpoint you will receive information about the SERP API tasks that returned an error within the past 7 days.\n- [SERP API Page Screenshot](https://docs.dataforseo.com/v3/serp/screenshot.md) — Using the Live Page Screenshot endpoint, you can capture a screenshot of any SERP page.\n- [SERP API AI Summary](https://docs.dataforseo.com/v3/serp/ai_summary.md) — The purpose of the Live SERP API AI Summary endpoint is to provide a summary of the content found on any SERP and generate a response based on the user’s specified prompt.\n\n\n#", "source": "llm_documentation"}, {"endpoint": "/serp/endpoints", "title": "here", "category": "SERP API", "description": "- [SERP API ID List](https://docs.dataforseo.com/v3/serp/id_list.md) — This endpoint is designed to provide you with the list of IDs and metadata of the completed SERP tasks during the specified period. You will get all task IDs that were made including successful, uncompleted, and tasks that responded as errors. - [SERP API Errors](https://docs.dataforseo.com/v3/serp/errors.md) — By calling this endpoint you will receive information about the SERP API tasks that returned an error within the past 7 days. - [SERP API Page Screenshot](https://docs.dataforseo.com/v3/serp/screenshot.md) — Using the Live Page Screenshot endpoint, you can capture a screenshot of any SERP page.", "url": "https://docs.dataforseo.com/v3/serp/endpoints.md", "content": "\n- [SERP API: Overview](https://docs.dataforseo.com/v3/serp/overview.md) — SERP API encompasses a broad range of endpoints. You can get the full list [here](https://docs.dataforseo.com/v3/serp/endpoints.md).\n- [SERP API ID List](https://docs.dataforseo.com/v3/serp/id_list.md) — This endpoint is designed to provide you with the list of IDs and metadata of the completed SERP tasks during the specified period. You will get all task IDs that were made including successful, uncompleted, and tasks that responded as errors.\n- [SERP API Errors](https://docs.dataforseo.com/v3/serp/errors.md) — By calling this endpoint you will receive information about the SERP API tasks that returned an error within the past 7 days.\n- [SERP API Page Screenshot](https://docs.dataforseo.com/v3/serp/screenshot.md) — Using the Live Page Screenshot endpoint, you can capture a screenshot of any SERP page.\n- [SERP API AI Summary](https://docs.dataforseo.com/v3/serp/ai_summary.md) — The purpose of the Live SERP API AI Summary endpoint is to provide a summary of the content found on any SERP and generate a response based on the user’s specified prompt.\n\n\n#", "source": "llm_documentation"}]}