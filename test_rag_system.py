#!/usr/bin/env python3
"""
Test the RAG-powered DataForSEO system
Demonstrates semantic search over API documentation
"""

import os
import sys
sys.path.append('.')

from tools.dataforseo_rag_system import search_dataforseo_documentation, get_endpoint_documentation
from tools.direct_dataforseo_client import intelligent_dataforseo_query

def test_semantic_documentation_search():
    """Test semantic search over DataForSEO documentation"""
    
    print("🧪 Testing RAG-Powered Documentation Search")
    print("=" * 60)
    
    test_queries = [
        "find semantically related keywords for content marketing",
        "get search volume data for multiple keywords",
        "analyze competitor domain rankings and metrics", 
        "check backlink profile and referring domains",
        "get Google Maps local search results for restaurants",
        "analyze on-page SEO technical issues",
        "find what keywords a competitor website ranks for",
        "get keyword difficulty scores for my keyword list",
        "analyze Google Images search results",
        "get domain analytics and technology stack information"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: '{query}'")
        print("-" * 50)
        
        try:
            result = search_dataforseo_documentation(
                search_query=query,
                max_results=2
            )
            
            best_match = result['best_match']
            print(f"✅ Best Match: {best_match['endpoint']}")
            print(f"   Confidence: {best_match.get('confidence', 0):.3f}")
            print(f"   Description: {best_match['description'][:80]}...")
            print(f"   Use Cases: {', '.join(best_match['use_cases'][:2])}")
            
            if result['alternatives']:
                alt = result['alternatives'][0]
                print(f"   Alternative: {alt['endpoint']} (confidence: {alt['confidence']:.3f})")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_intelligent_dataforseo_query():
    """Test the intelligent RAG-powered DataForSEO query tool"""
    
    print("\n🤖 Testing Intelligent DataForSEO Query (RAG-Powered)")
    print("=" * 60)
    
    # Check credentials
    username = os.getenv('DATAFORSEO_USERNAME') or os.getenv('DATAFORSEO_LOGIN')
    password = os.getenv('DATAFORSEO_PASSWORD')
    
    if not username or not password:
        print("⚠️  DataForSEO credentials not found!")
        print("Please set DATAFORSEO_USERNAME and DATAFORSEO_PASSWORD in .env file")
        print("For now, testing will show connection errors\n")
    else:
        print(f"✅ Credentials found for user: {username[:3]}...{username[-3:]}")
    
    # Test 1: Natural language keyword research
    print("\n1. Testing: Natural language keyword research")
    print("-" * 50)
    try:
        result = intelligent_dataforseo_query(
            natural_language_query="I need to find semantically related keywords for content marketing to expand my keyword list",
            target_keyword="content marketing",
            geographic_location="United States",
            result_limit=10
        )
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            if 'suggestion' in result:
                print(f"   Suggestion: {result['suggestion']}")
        else:
            print(f"✅ Success!")
            endpoint_info = result['endpoint_info']
            print(f"   Selected Endpoint: {endpoint_info['selected_endpoint']}")
            print(f"   Confidence Score: {endpoint_info['confidence_score']:.3f}")
            print(f"   Selection Reasoning: {endpoint_info['selection_reasoning']}")
            print(f"   Parameters Used: {list(endpoint_info['parameters_used'].keys())}")
            
            # Check API response
            api_response = result['api_response']
            if 'tasks' in api_response and api_response['tasks']:
                task = api_response['tasks'][0]
                if 'result' in task and task['result']:
                    keywords = task['result']
                    print(f"   Found {len(keywords)} related keywords")
                    for kw in keywords[:3]:
                        keyword = kw.get('keyword', 'N/A')
                        volume = kw.get('search_volume', 'N/A')
                        print(f"     • {keyword} (Volume: {volume})")
                        
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Competitor analysis with domain extraction
    print("\n2. Testing: Competitor analysis with automatic domain extraction")
    print("-" * 50)
    try:
        result = intelligent_dataforseo_query(
            natural_language_query="I want to analyze what keywords semrush.com ranks for to understand their SEO strategy",
            geographic_location="United States",
            result_limit=5
        )
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
        else:
            print(f"✅ Success!")
            endpoint_info = result['endpoint_info']
            print(f"   Selected Endpoint: {endpoint_info['selected_endpoint']}")
            print(f"   Confidence Score: {endpoint_info['confidence_score']:.3f}")
            print(f"   Extracted Domain: {endpoint_info['parameters_used'].get('target', 'N/A')}")
            print(f"   Use Cases: {', '.join(endpoint_info['use_cases'][:2])}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 3: Search volume analysis
    print("\n3. Testing: Search volume analysis with keyword list")
    print("-" * 50)
    try:
        result = intelligent_dataforseo_query(
            natural_language_query="Get search volume data for my keyword list to prioritize content creation",
            target_keywords=["seo tools", "keyword research", "content marketing"],
            geographic_location="United States"
        )
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
        else:
            print(f"✅ Success!")
            endpoint_info = result['endpoint_info']
            print(f"   Selected Endpoint: {endpoint_info['selected_endpoint']}")
            print(f"   Keywords Analyzed: {endpoint_info['parameters_used'].get('keywords', [])}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_endpoint_documentation():
    """Test getting specific endpoint documentation"""
    
    print("\n📚 Testing Endpoint Documentation Retrieval")
    print("=" * 60)
    
    endpoints_to_test = [
        "/dataforseo_labs/google/related_keywords/live",
        "/serp/google/organic/live/advanced",
        "/backlinks/summary/live"
    ]
    
    for endpoint in endpoints_to_test:
        print(f"\nEndpoint: {endpoint}")
        print("-" * 40)
        
        try:
            doc = get_endpoint_documentation(endpoint_path=endpoint)
            
            if 'error' in doc:
                print(f"❌ Error: {doc['error']}")
            else:
                print(f"✅ Description: {doc['description'][:60]}...")
                print(f"   Required Parameters: {doc['required_parameters']}")
                print(f"   Optional Parameters: {doc['optional_parameters'][:3]}...")
                print(f"   Use Cases: {', '.join(doc['use_cases'][:2])}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def demonstrate_rag_benefits():
    """Demonstrate the benefits of the RAG approach"""
    
    print("\n🎯 RAG System Benefits Demonstration")
    print("=" * 60)
    
    print("\n🔍 Semantic Understanding Examples:")
    
    semantic_examples = [
        {
            "query": "find similar keywords",
            "matches": "related_keywords endpoint (semantic similarity)"
        },
        {
            "query": "competitor keyword analysis", 
            "matches": "ranked_keywords endpoint (competitor + keyword context)"
        },
        {
            "query": "search volume metrics",
            "matches": "search_volume endpoint (volume + metrics context)"
        },
        {
            "query": "backlink profile analysis",
            "matches": "backlinks/summary endpoint (profile + analysis context)"
        }
    ]
    
    for example in semantic_examples:
        print(f"• '{example['query']}' → {example['matches']}")
    
    print("\n🚀 RAG vs Traditional Approaches:")
    print("┌─────────────────────┬─────────────────────┬─────────────────────┐")
    print("│ Approach            │ Accuracy            │ Flexibility         │")
    print("├─────────────────────┼─────────────────────┼─────────────────────┤")
    print("│ Hardcoded Rules     │ Limited patterns    │ Rigid, hard to extend│")
    print("│ Keyword Matching    │ Exact matches only  │ Brittle, misses nuance│")
    print("│ RAG Semantic Search │ High semantic match │ Learns from context │")
    print("└─────────────────────┴─────────────────────┴─────────────────────┘")
    
    print("\n✅ RAG System Advantages:")
    print("• Semantic understanding of user intent")
    print("• Confidence scores for endpoint selection")
    print("• Automatic parameter extraction and validation")
    print("• Comprehensive documentation context")
    print("• Extensible without code changes")
    print("• Prevents hallucination through grounded retrieval")

if __name__ == "__main__":
    test_semantic_documentation_search()
    test_intelligent_dataforseo_query()
    test_endpoint_documentation()
    demonstrate_rag_benefits()
    
    print("\n" + "=" * 60)
    print("🎯 RAG-Powered DataForSEO System Summary:")
    print("✅ Semantic search over comprehensive API documentation")
    print("✅ Intelligent endpoint selection with confidence scores")
    print("✅ Automatic parameter extraction from natural language")
    print("✅ Real-time API calls with validated parameters")
    print("✅ Prevents LLM hallucination through grounded retrieval")
    print("✅ Extensible and maintainable architecture")
    print("\n💡 The LLM now has sophisticated semantic understanding!")
    print("=" * 60)
